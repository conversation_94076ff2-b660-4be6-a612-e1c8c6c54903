#!/usr/bin/env python3
"""
Correct Shopify CSV conversion - properly groups variants by product
"""

import csv
import re
from collections import defaultdict

def create_handle(title, sku_base):
    """Create a clean handle"""
    if 'سهرة' in title:
        base = 'evening-bag'
    elif 'كتف' in title:
        base = 'shoulder-bag'
    elif 'يد' in title:
        base = 'handbag'
    elif 'كروس' in title or 'كورس' in title:
        base = 'crossbody-bag'
    elif 'باوتش' in title:
        base = 'pouch-bag'
    else:
        base = 'bag'
    
    sku_clean = re.sub(r'[^\w]', '', str(sku_base).lower())
    return f"{base}-{sku_clean}"

def get_sku_base(sku):
    """Extract base SKU by removing color suffixes"""
    sku_lower = sku.lower()
    # Remove common color suffixes
    color_patterns = [
        r'black$', r'pink$', r'gold$', r'silver$', r'green$', r'blue$', 
        r'navy$', r'brown$', r'cream$', r'champagne$', r'gun$', r'beige$', 
        r'begie$', r'off-white$', r'offwhite$', r'darkgreen$', r'siver$',
        r'silvar$', r'hsil;ver$'
    ]
    
    for pattern in color_patterns:
        sku_lower = re.sub(pattern, '', sku_lower)
    
    return sku_lower

def main():
    # Read and group products by SKU base
    product_groups = defaultdict(list)
    
    with open('cb-sfy.csv', 'r', encoding='utf-8') as infile:
        # Skip header
        next(infile)
        reader = csv.reader(infile)
        
        for row in reader:
            if len(row) >= 12:
                product = {
                    'gtin': row[0],
                    'mpn': row[1],
                    'title': row[2],
                    'category': row[3],
                    'image_src': row[4],
                    'body_html': row[5],
                    'color_field': row[6] if len(row) > 6 else '',
                    'option1_name': row[7] if len(row) > 7 else '',
                    'option1_value': row[8] if len(row) > 8 else '',
                    'variant_image': row[9] if len(row) > 9 else '',
                    'variant_price': row[10] if len(row) > 10 else '',
                    'variant_sku': row[11] if len(row) > 11 else ''
                }
                
                # Group by SKU base
                sku_base = get_sku_base(product['variant_sku'])
                product_groups[sku_base].append(product)
    
    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
        'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
        'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Write the corrected CSV
    with open('cb-sfy-corrected.csv', 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(headers)
        
        product_count = 0
        variant_count = 0
        
        for sku_base, products in product_groups.items():
            if not products:
                continue
            
            # Find the main product (منتج)
            main_product = None
            variants = []
            
            for product in products:
                if product['mpn'] == 'منتج':
                    main_product = product
                elif product['mpn'] == 'خيار':
                    variants.append(product)
            
            # Skip if no main product found
            if not main_product:
                continue
            
            # Create handle
            handle = create_handle(main_product['title'], sku_base)
            
            # Clean category
            category = main_product['category'] if main_product['category'] else 'حقائب'
            if 'أطفال' in category:
                category = 'حقائب'
            
            # Process main product images
            main_images = []
            if main_product['image_src']:
                main_images = [img.strip() for img in main_product['image_src'].split(',') 
                             if img.strip() and img.strip().startswith('http')]
            
            product_count += 1
            image_position = 1
            first_variant = True
            
            # Add variants
            for variant in variants:
                variant_count += 1
                
                # Check if variant has valid image
                has_variant_image = (variant['variant_image'] and 
                                   variant['variant_image'].startswith('http') and
                                   variant['variant_image'] != '#000000')
                
                row = [
                    handle,  # Handle
                    main_product['title'] if first_variant else '',  # Title
                    main_product['body_html'] if first_variant else '',  # Body (HTML)
                    'ChrisBella',  # Vendor
                    category if first_variant else '',  # Product Category
                    'Bags' if first_variant else '',  # Type
                    'حقائب, إكسسوارات, نسائية' if first_variant else '',  # Tags
                    'TRUE' if first_variant else '',  # Published
                    'Color' if variant['option1_value'] else '',  # Option1 Name
                    variant['option1_value'],  # Option1 Value
                    '',  # Option2 Name
                    '',  # Option2 Value
                    '',  # Option3 Name
                    '',  # Option3 Value
                    variant['variant_sku'],  # Variant SKU
                    '0',  # Variant Grams
                    'shopify',  # Variant Inventory Tracker
                    '100',  # Variant Inventory Qty
                    'deny',  # Variant Inventory Policy
                    'manual',  # Variant Fulfillment Service
                    variant['variant_price'],  # Variant Price
                    '',  # Variant Compare At Price
                    'TRUE',  # Variant Requires Shipping
                    'TRUE',  # Variant Taxable
                    variant['gtin'],  # Variant Barcode
                    variant['variant_image'] if has_variant_image else '',  # Image Src
                    str(image_position) if has_variant_image else '',  # Image Position
                    f"{main_product['title']} {variant['option1_value']}".strip(),  # Image Alt Text
                    'FALSE',  # Gift Card
                    f"{main_product['title']} - ChrisBella" if first_variant else '',  # SEO Title
                    f"{main_product['title']} من ChrisBella" if first_variant else '',  # SEO Description
                    'Apparel & Accessories > Handbags' if first_variant else '',  # Google Shopping Category
                    'Female' if first_variant else '',  # Google Shopping Gender
                    'Adult' if first_variant else '',  # Google Shopping Age Group
                    sku_base if first_variant else '',  # Google Shopping MPN
                    'Bags' if first_variant else '',  # Google Shopping AdWords Grouping
                    'Luxury Bags' if first_variant else '',  # Google Shopping AdWords Labels
                    'New' if first_variant else '',  # Google Shopping Condition
                    'FALSE' if first_variant else '',  # Google Shopping Custom Product
                    'Bags' if first_variant else '',  # Google Shopping Custom Label 0
                    'Luxury' if first_variant else '',  # Google Shopping Custom Label 1
                    variant['option1_value'] if variant['option1_value'] else '',  # Google Shopping Custom Label 2
                    'ChrisBella' if first_variant else '',  # Google Shopping Custom Label 3
                    category if first_variant else '',  # Google Shopping Custom Label 4
                    variant['variant_image'] if has_variant_image else '',  # Variant Image
                    'kg',  # Variant Weight Unit
                    'active'  # Status
                ]
                
                writer.writerow(row)
                
                if has_variant_image:
                    image_position += 1
                
                first_variant = False
            
            # Add additional main product images (if any)
            for img_url in main_images:
                if img_url and img_url.startswith('http'):
                    img_row = [handle] + [''] * 25 + [img_url, str(image_position), main_product['title']] + [''] * 16
                    writer.writerow(img_row)
                    image_position += 1
        
        print(f"Conversion completed!")
        print(f"Products: {product_count}")
        print(f"Variants: {variant_count}")
        print(f"Average variants per product: {variant_count/product_count:.1f}")

if __name__ == "__main__":
    main()
