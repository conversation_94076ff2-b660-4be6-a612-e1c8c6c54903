#!/usr/bin/env python3
"""
Simple script to fix the Shopify CSV file
"""

import csv
import re

def clean_text(text):
    """Clean text for CSV"""
    if not text:
        return ""
    return text.strip().strip('"')

def create_handle(title, sku):
    """Create a clean handle"""
    # Use English words for handle
    if 'سهرة' in title:
        base = 'evening-bag'
    elif 'كتف' in title:
        base = 'shoulder-bag'
    elif 'يد' in title:
        base = 'handbag'
    elif 'كروس' in title or 'كورس' in title:
        base = 'crossbody-bag'
    elif 'باوتش' in title:
        base = 'pouch-bag'
    else:
        base = 'bag'
    
    # Clean SKU for handle
    sku_clean = re.sub(r'[^\w]', '', str(sku).lower())
    return f"{base}-{sku_clean}"

def main():
    # Read original file and create a clean version
    with open('cb-sfy.csv', 'r', encoding='utf-8') as infile:
        lines = infile.readlines()
    
    # Skip the header we already modified
    data_lines = lines[1:]
    
    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
        'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
        'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Write clean CSV
    with open('cb-sfy-clean.csv', 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(headers)
        
        # Process each line
        for line in data_lines:
            line = line.strip()
            if not line:
                continue
                
            # Simple split by comma (this will work for most cases)
            parts = line.split(',')
            
            if len(parts) >= 12:
                gtin = clean_text(parts[0])
                mpn = clean_text(parts[1])
                title = clean_text(parts[2])
                category = clean_text(parts[3])
                image_src = clean_text(parts[4])
                body_html = clean_text(parts[5])
                color_field = clean_text(parts[6]) if len(parts) > 6 else ''
                option1_name = clean_text(parts[7]) if len(parts) > 7 else ''
                option1_value = clean_text(parts[8]) if len(parts) > 8 else ''
                variant_image = clean_text(parts[9]) if len(parts) > 9 else ''
                variant_price = clean_text(parts[10]) if len(parts) > 10 else ''
                variant_sku = clean_text(parts[11]) if len(parts) > 11 else ''
                
                # Skip if this is a variant (خيار) without main product info
                if mpn == 'خيار' and not title:
                    continue
                
                # Create handle
                handle = create_handle(title, variant_sku)
                
                # Clean category
                if not category or 'أطفال' in category:
                    category = 'حقائب'
                
                # Check if image is valid
                has_image = variant_image and variant_image.startswith('http')
                
                # Create row
                row = [
                    handle,  # Handle
                    title,  # Title
                    body_html,  # Body (HTML)
                    'ChrisBella',  # Vendor
                    category,  # Product Category
                    'Bags',  # Type
                    'حقائب, إكسسوارات, نسائية',  # Tags
                    'TRUE',  # Published
                    'Color' if option1_value else '',  # Option1 Name
                    option1_value,  # Option1 Value
                    '',  # Option2 Name
                    '',  # Option2 Value
                    '',  # Option3 Name
                    '',  # Option3 Value
                    variant_sku,  # Variant SKU
                    '0',  # Variant Grams
                    'shopify',  # Variant Inventory Tracker
                    '100',  # Variant Inventory Qty
                    'deny',  # Variant Inventory Policy
                    'manual',  # Variant Fulfillment Service
                    variant_price,  # Variant Price
                    '',  # Variant Compare At Price
                    'TRUE',  # Variant Requires Shipping
                    'TRUE',  # Variant Taxable
                    gtin,  # Variant Barcode
                    variant_image if has_image else '',  # Image Src
                    '1' if has_image else '',  # Image Position
                    f"{title} {option1_value}".strip(),  # Image Alt Text
                    'FALSE',  # Gift Card
                    f"{title} - ChrisBella",  # SEO Title
                    f"{title} من ChrisBella",  # SEO Description
                    'Apparel & Accessories > Handbags',  # Google Shopping Category
                    'Female',  # Google Shopping Gender
                    'Adult',  # Google Shopping Age Group
                    variant_sku,  # Google Shopping MPN
                    'Bags',  # Google Shopping AdWords Grouping
                    'Luxury Bags',  # Google Shopping AdWords Labels
                    'New',  # Google Shopping Condition
                    'FALSE',  # Google Shopping Custom Product
                    'Bags',  # Google Shopping Custom Label 0
                    'Luxury',  # Google Shopping Custom Label 1
                    option1_value,  # Google Shopping Custom Label 2
                    'ChrisBella',  # Google Shopping Custom Label 3
                    category,  # Google Shopping Custom Label 4
                    variant_image if has_image else '',  # Variant Image
                    'kg',  # Variant Weight Unit
                    'active'  # Status
                ]
                
                writer.writerow(row)

if __name__ == "__main__":
    main()
    print("Clean CSV created: cb-sfy-clean.csv")
