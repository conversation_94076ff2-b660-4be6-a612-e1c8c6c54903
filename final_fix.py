#!/usr/bin/env python3
"""
Final fix for Shopify CSV - handles the original format properly
"""

import csv
import re

def create_handle(title, sku):
    """Create a clean handle"""
    if 'سهرة' in title:
        base = 'evening-bag'
    elif 'كتف' in title:
        base = 'shoulder-bag'
    elif 'يد' in title:
        base = 'handbag'
    elif 'كروس' in title or 'كورس' in title:
        base = 'crossbody-bag'
    elif 'باوتش' in title:
        base = 'pouch-bag'
    else:
        base = 'bag'
    
    sku_clean = re.sub(r'[^\w]', '', str(sku).lower())
    return f"{base}-{sku_clean}"

def main():
    # Read the original file using CSV reader to handle quoted fields properly
    products = []
    
    with open('cb-sfy.csv', 'r', encoding='utf-8') as infile:
        # Skip the header line manually
        next(infile)
        
        # Read the rest using csv.reader
        reader = csv.reader(infile)
        
        for row in reader:
            if len(row) >= 12:
                products.append({
                    'gtin': row[0],
                    'mpn': row[1],
                    'title': row[2],
                    'category': row[3],
                    'image_src': row[4],
                    'body_html': row[5],
                    'color_field': row[6] if len(row) > 6 else '',
                    'option1_name': row[7] if len(row) > 7 else '',
                    'option1_value': row[8] if len(row) > 8 else '',
                    'variant_image': row[9] if len(row) > 9 else '',
                    'variant_price': row[10] if len(row) > 10 else '',
                    'variant_sku': row[11] if len(row) > 11 else ''
                })
    
    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
        'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
        'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Write the final clean CSV
    with open('cb-sfy-final.csv', 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(headers)
        
        for product in products:
            # Skip empty products
            if not product['title'] and not product['variant_sku']:
                continue
            
            # Create handle
            handle = create_handle(product['title'], product['variant_sku'])
            
            # Clean category
            category = product['category'] if product['category'] else 'حقائب'
            if 'أطفال' in category:
                category = 'حقائب'
            
            # Check if image is valid
            has_image = (product['variant_image'] and 
                        product['variant_image'].startswith('http') and
                        product['variant_image'] != '#000000')
            
            # Only include products with meaningful data
            if product['mpn'] == 'خيار' and not product['title']:
                # This is a variant without main product info, skip
                continue
            
            # Create row
            row = [
                handle,  # Handle
                product['title'],  # Title
                product['body_html'],  # Body (HTML)
                'ChrisBella',  # Vendor
                category,  # Product Category
                'Bags',  # Type
                'حقائب, إكسسوارات, نسائية',  # Tags
                'TRUE',  # Published
                'Color' if product['option1_value'] else '',  # Option1 Name
                product['option1_value'],  # Option1 Value
                '',  # Option2 Name
                '',  # Option2 Value
                '',  # Option3 Name
                '',  # Option3 Value
                product['variant_sku'],  # Variant SKU
                '0',  # Variant Grams
                'shopify',  # Variant Inventory Tracker
                '100',  # Variant Inventory Qty
                'deny',  # Variant Inventory Policy
                'manual',  # Variant Fulfillment Service
                product['variant_price'],  # Variant Price
                '',  # Variant Compare At Price
                'TRUE',  # Variant Requires Shipping
                'TRUE',  # Variant Taxable
                product['gtin'],  # Variant Barcode
                product['variant_image'] if has_image else '',  # Image Src
                '1' if has_image else '',  # Image Position
                f"{product['title']} {product['option1_value']}".strip(),  # Image Alt Text
                'FALSE',  # Gift Card
                f"{product['title']} - ChrisBella" if product['title'] else '',  # SEO Title
                f"{product['title']} من ChrisBella" if product['title'] else '',  # SEO Description
                'Apparel & Accessories > Handbags',  # Google Shopping Category
                'Female',  # Google Shopping Gender
                'Adult',  # Google Shopping Age Group
                product['variant_sku'],  # Google Shopping MPN
                'Bags',  # Google Shopping AdWords Grouping
                'Luxury Bags',  # Google Shopping AdWords Labels
                'New',  # Google Shopping Condition
                'FALSE',  # Google Shopping Custom Product
                'Bags',  # Google Shopping Custom Label 0
                'Luxury',  # Google Shopping Custom Label 1
                product['option1_value'],  # Google Shopping Custom Label 2
                'ChrisBella',  # Google Shopping Custom Label 3
                category,  # Google Shopping Custom Label 4
                product['variant_image'] if has_image else '',  # Variant Image
                'kg',  # Variant Weight Unit
                'active'  # Status
            ]
            
            writer.writerow(row)

if __name__ == "__main__":
    main()
    print("Final clean CSV created: cb-sfy-final.csv")
