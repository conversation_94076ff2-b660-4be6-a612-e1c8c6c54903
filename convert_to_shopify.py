#!/usr/bin/env python3
"""
<PERSON><PERSON>t to convert Salla CSV format to Shopify CSV format
"""

import csv
import re
from collections import defaultdict

def clean_html_description(html_text):
    """Clean and format HTML description for Shopify"""
    if not html_text:
        return ""

    # Remove extra quotes and escape internal quotes
    html_text = html_text.strip()
    if html_text.startswith('"') and html_text.endswith('"'):
        html_text = html_text[1:-1]

    # Replace double quotes with single quotes in HTML
    html_text = html_text.replace('""', '"')

    return html_text

def create_shopify_handle(title, sku):
    """Create a Shopify handle from title and SKU"""
    # Use SKU as base for handle to ensure uniqueness
    sku_clean = re.sub(r'[^\w]', '', str(sku).lower())
    title_clean = re.sub(r'[^\w\s-]', '', title.lower())
    title_clean = re.sub(r'\s+', '-', title_clean.strip())

    # Combine title and SKU for unique handle
    if title_clean and len(title_clean) > 2:
        handle = f"{title_clean}-{sku_clean}"
    else:
        handle = f"product-{sku_clean}"

    return handle[:100]  # Shopify handle limit

def split_images(image_string):
    """Split comma-separated images into individual URLs"""
    if not image_string:
        return []

    # Split by comma and clean each URL
    images = [img.strip() for img in image_string.split(',') if img.strip() and img.strip().startswith('http')]
    return images

def parse_csv_line(line):
    """Parse CSV line handling embedded commas in quoted fields"""
    parts = []
    current_part = ""
    in_quotes = False

    i = 0
    while i < len(line):
        char = line[i]

        if char == '"':
            in_quotes = not in_quotes
            current_part += char
        elif char == ',' and not in_quotes:
            parts.append(current_part.strip())
            current_part = ""
        else:
            current_part += char
        i += 1

    # Add the last part
    if current_part:
        parts.append(current_part.strip())

    return parts

def convert_salla_to_shopify():
    """Convert Salla CSV to Shopify format"""

    # Read the original file and parse properly
    products = defaultdict(list)

    with open('cb-sfy.csv', 'r', encoding='utf-8') as file:
        # Skip the header line
        next(file)

        for line_num, line in enumerate(file, 2):
            line = line.strip()
            if not line:
                continue

            try:
                parts = parse_csv_line(line)

                if len(parts) >= 12:
                    # Extract data from original format
                    row_data = {
                        'gtin': parts[0].strip('"'),
                        'mpn': parts[1].strip('"'),
                        'title': parts[2].strip('"'),
                        'category': parts[3].strip('"'),
                        'image_src': parts[4].strip('"'),
                        'body_html': parts[5].strip('"'),
                        'color_field': parts[6].strip('"') if len(parts) > 6 else '',
                        'option1_name': parts[7].strip('"') if len(parts) > 7 else '',
                        'option1_value': parts[8].strip('"') if len(parts) > 8 else '',
                        'variant_image': parts[9].strip('"') if len(parts) > 9 else '',
                        'variant_price': parts[10].strip('"') if len(parts) > 10 else '',
                        'variant_sku': parts[11].strip('"') if len(parts) > 11 else ''
                    }

                    # Group by SKU base (remove color suffix)
                    sku_base = re.sub(r'(black|pink|gold|silver|green|blue|navy|brown|cream|champagne|gun|beige|begie|off-white|dark.*?)$', '', row_data['variant_sku'].lower(), flags=re.IGNORECASE)
                    if not sku_base:
                        sku_base = row_data['variant_sku']

                    products[sku_base].append(row_data)

            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue

    # Write new Shopify CSV
    shopify_headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
        'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
        'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]

    with open('cb-sfy-shopify-fixed.csv', 'w', encoding='utf-8', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(shopify_headers)

        for sku_base, product_variants in products.items():
            if not product_variants:
                continue

            # Find the main product (منتج) or use first variant
            main_product = None
            for variant in product_variants:
                if variant['mpn'] == 'منتج':
                    main_product = variant
                    break

            if not main_product:
                main_product = product_variants[0]

            # Create handle
            handle = create_shopify_handle(main_product['title'], sku_base)

            # Clean category
            category = main_product['category'] if main_product['category'] else 'حقائب'
            if 'أطفال' in category:
                category = 'حقائب'

            # Process main product images
            main_images = split_images(main_product['image_src'])

            # First row with full product info
            first_variant = True
            image_position = 1

            # Process variants
            for variant in product_variants:
                # Skip if this is not a variant (خيار) and not the first variant
                if variant['mpn'] != 'خيار' and not first_variant:
                    continue

                # Determine color
                color = variant['option1_value'] if variant['option1_value'] else ''

                # Check if variant has valid image
                has_variant_image = (variant['variant_image'] and
                                   variant['variant_image'] != '#000000' and
                                   variant['variant_image'].startswith('http'))

                row = [
                    handle,  # Handle
                    main_product['title'] if first_variant else '',  # Title
                    clean_html_description(main_product['body_html']) if first_variant else '',  # Body (HTML)
                    'ChrisBella',  # Vendor
                    category if first_variant else '',  # Product Category
                    'Bags' if first_variant else '',  # Type
                    'حقائب, إكسسوارات, نسائية' if first_variant else '',  # Tags
                    'TRUE' if first_variant else '',  # Published
                    'Color' if color else '',  # Option1 Name
                    color,  # Option1 Value
                    '',  # Option2 Name
                    '',  # Option2 Value
                    '',  # Option3 Name
                    '',  # Option3 Value
                    variant['variant_sku'],  # Variant SKU
                    '0',  # Variant Grams
                    'shopify',  # Variant Inventory Tracker
                    '100',  # Variant Inventory Qty
                    'deny',  # Variant Inventory Policy
                    'manual',  # Variant Fulfillment Service
                    variant['variant_price'],  # Variant Price
                    '',  # Variant Compare At Price
                    'TRUE',  # Variant Requires Shipping
                    'TRUE',  # Variant Taxable
                    variant['gtin'],  # Variant Barcode
                    variant['variant_image'] if has_variant_image else '',  # Image Src
                    str(image_position) if has_variant_image else '',  # Image Position
                    f"{main_product['title']} {color}".strip(),  # Image Alt Text
                    'FALSE',  # Gift Card
                    f"{main_product['title']} - ChrisBella" if first_variant else '',  # SEO Title
                    f"{main_product['title']} من ChrisBella" if first_variant else '',  # SEO Description
                    'Apparel & Accessories > Handbags' if first_variant else '',  # Google Shopping Category
                    'Female' if first_variant else '',  # Google Shopping Gender
                    'Adult' if first_variant else '',  # Google Shopping Age Group
                    sku_base if first_variant else '',  # Google Shopping MPN
                    'Bags' if first_variant else '',  # Google Shopping AdWords Grouping
                    'Luxury Bags' if first_variant else '',  # Google Shopping AdWords Labels
                    'New' if first_variant else '',  # Google Shopping Condition
                    'FALSE' if first_variant else '',  # Google Shopping Custom Product
                    'Bags' if first_variant else '',  # Google Shopping Custom Label 0
                    'Luxury' if first_variant else '',  # Google Shopping Custom Label 1
                    color if color else '',  # Google Shopping Custom Label 2
                    'ChrisBella' if first_variant else '',  # Google Shopping Custom Label 3
                    category if first_variant else '',  # Google Shopping Custom Label 4
                    variant['variant_image'] if has_variant_image else '',  # Variant Image
                    'kg',  # Variant Weight Unit
                    'active'  # Status
                ]

                writer.writerow(row)

                if has_variant_image:
                    image_position += 1

                first_variant = False

            # Add additional main product images
            for img_url in main_images:
                if img_url and img_url.startswith('http'):
                    img_row = [handle] + [''] * 25 + [img_url, str(image_position), main_product['title']] + [''] * 16
                    writer.writerow(img_row)
                    image_position += 1

if __name__ == "__main__":
    convert_salla_to_shopify()
    print("Conversion completed! Check cb-sfy-shopify-fixed.csv")
