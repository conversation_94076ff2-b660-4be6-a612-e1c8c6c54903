#!/usr/bin/env python3
"""
Complete Shopify conversion with proper variants structure like SHOPIFY-READY.csv
"""

import csv
import re
from collections import defaultdict

def create_handle(title, base_sku):
    """Create a clean handle from title and base SKU"""
    # Use English words for handle
    if 'سهرة' in title:
        base = 'evening-bag'
    elif 'كتف' in title:
        base = 'shoulder-bag'
    elif 'يد' in title and 'توتي' not in title:
        base = 'handbag'
    elif 'كروس' in title or 'كورس' in title:
        base = 'crossbody-bag'
    elif 'باوتش' in title:
        base = 'pouch-bag'
    elif 'توتي' in title:
        base = 'tote-bag'
    elif 'حروف' in title:
        base = 'letters'
    elif 'سيفين' in title:
        base = 'seven-palm'
    elif 'كرز' in title:
        base = 'cherry'
    elif 'جمل' in title:
        base = 'camel'
    else:
        base = 'bag'
    
    # Clean base SKU for handle
    sku_clean = re.sub(r'[^\w]', '', str(base_sku).lower())
    return f"{base}-{sku_clean}"

def extract_base_sku(sku):
    """Extract base SKU by removing color suffixes"""
    sku_str = str(sku).strip()
    
    # Common color suffixes to remove
    color_suffixes = [
        'black', 'white', 'brown', 'beige', 'navy', 'coffee', 'cream',
        'khaki', 'pink', 'green', 'blue', 'red', 'gray', 'grey', 'purple',
        'wine red', 'winered', 'win red', 'off white', 'offwhite', 'off-white',
        'l pink', 'lpink', 'l green', 'lgreen', 'army green', 'armygreen',
        'biue', 'biege', 'winred', 'maroon', 'sand', 'mud', 'gold', 'silver',
        'champagne', 'apricot', 'off whit', 'offwhit', 'h', 'H'
    ]
    
    # Remove color suffixes
    base = sku_str
    for suffix in color_suffixes:
        # Remove suffix at the end
        if base.lower().endswith(suffix.lower()):
            base = base[:-len(suffix)].rstrip('-_ ')
        # Remove suffix with separators
        for sep in ['-', '_', ' ']:
            pattern = sep + suffix
            if base.lower().endswith(pattern.lower()):
                base = base[:-len(pattern)]
    
    return base.strip()

def main():
    # Read original Salla export
    all_products = []
    
    with open('chrisbella_24-08-2025-12-00_jCmuZwaX8DClwrXDKhYVyL1rSD1f62pl9wUURec4_products.csv', 'r', encoding='utf-8') as infile:
        reader = csv.reader(infile)
        header = next(reader)  # Skip header
        
        for row in reader:
            if len(row) >= 24:  # Ensure we have enough columns
                # Extract relevant data from Salla format
                gtin = row[0]
                mpn = row[1]  # "منتج" or "خيار"
                title = row[2]  # Product title
                category = row[3] if len(row) > 3 else ''
                image_src = row[4] if len(row) > 4 else ''
                description = row[5] if len(row) > 5 else ''
                color_value = row[11] if len(row) > 11 else ''  # [2] القيمة (Color value)
                variant_image = row[12] if len(row) > 12 else ''  # [2] الصورة (Color image)
                price = row[21] if len(row) > 21 else ''  # السعر
                sku = row[23] if len(row) > 23 else ''  # رمز المنتج sku
                
                # Only process products with meaningful data
                if title and sku and price:
                    product = {
                        'gtin': gtin,
                        'mpn': mpn,
                        'title': title,
                        'category': category,
                        'image_src': image_src,
                        'description': description,
                        'color_value': color_value,
                        'variant_image': variant_image,
                        'price': price,
                        'sku': sku
                    }
                    all_products.append(product)
    
    # Group products by base SKU and title
    product_groups = defaultdict(list)
    
    for product in all_products:
        base_sku = extract_base_sku(product['sku'])
        # Create a key combining title and base SKU for grouping
        group_key = f"{product['title']}_{base_sku}"
        product_groups[group_key].append(product)
    
    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
        'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
        'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Write the complete Shopify CSV
    with open('SHOPIFY-COMPLETE-VARIANTS.csv', 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(headers)
        
        product_count = 0
        variant_count = 0
        tote_count = 0
        
        for group_key, products in product_groups.items():
            product_count += 1
            
            # Sort products to ensure consistent ordering
            products.sort(key=lambda x: x['sku'])
            
            # Get the main product (first one or one with description)
            main_product = products[0]
            for p in products:
                if p['description'] and p['description'].strip():
                    main_product = p
                    break
            
            # Count tote bags
            if 'توتي' in main_product['title']:
                tote_count += 1
            
            # Create handle
            base_sku = extract_base_sku(main_product['sku'])
            handle = create_handle(main_product['title'], base_sku)
            
            # Clean category
            category = main_product['category'] if main_product['category'] else 'حقائب'
            if 'أطفال' in category:
                category = 'حقائب'
            
            # Determine product type
            if 'توتي' in main_product['title']:
                product_type = 'Tote Bags'
            elif 'سهرة' in main_product['title']:
                product_type = 'Evening Bags'
            elif 'حروف' in main_product['title']:
                product_type = 'Letters'
            elif 'سيفين' in main_product['title'] or 'كرز' in main_product['title'] or 'جمل' in main_product['title']:
                product_type = 'Accessories'
            else:
                product_type = 'Bags'
            
            # Write main product row and variants (like SHOPIFY-READY.csv structure)
            for i, product in enumerate(products):
                variant_count += 1
                is_main = (i == 0)
                
                # Check if variant has valid image
                has_variant_image = (product['variant_image'] and 
                                   product['variant_image'].startswith('http'))
                
                # For main product, use full details; for variants, use empty fields
                if is_main:
                    # Main product row with full details
                    row = [
                        handle,  # Handle
                        main_product['title'],  # Title
                        main_product['description'],  # Body (HTML)
                        'ChrisBella',  # Vendor
                        category,  # Product Category
                        product_type,  # Type
                        'حقائب, إكسسوارات, نسائية',  # Tags
                        'TRUE',  # Published
                        'Color',  # Option1 Name
                        product['color_value'],  # Option1 Value
                        '',  # Option2 Name
                        '',  # Option2 Value
                        '',  # Option3 Name
                        '',  # Option3 Value
                        product['sku'],  # Variant SKU
                        '0',  # Variant Grams
                        'shopify',  # Variant Inventory Tracker
                        '100',  # Variant Inventory Qty
                        'deny',  # Variant Inventory Policy
                        'manual',  # Variant Fulfillment Service
                        product['price'],  # Variant Price
                        '',  # Variant Compare At Price
                        'TRUE',  # Variant Requires Shipping
                        'TRUE',  # Variant Taxable
                        product['gtin'],  # Variant Barcode
                        product['variant_image'] if has_variant_image else '',  # Image Src
                        '1' if has_variant_image else '',  # Image Position
                        f"{main_product['title']} {product['color_value']}".strip(),  # Image Alt Text
                        'FALSE',  # Gift Card
                        f"{main_product['title']} - ChrisBella",  # SEO Title
                        f"{main_product['title']} من ChrisBella",  # SEO Description
                        'Apparel & Accessories > Handbags' if product_type in ['Bags', 'Tote Bags', 'Evening Bags'] else 'Arts & Entertainment > Hobbies & Creative Arts > Arts & Crafts',  # Google Shopping Category
                        'Female',  # Google Shopping Gender
                        'Adult',  # Google Shopping Age Group
                        base_sku,  # Google Shopping MPN
                        product_type,  # Google Shopping AdWords Grouping
                        'Luxury',  # Google Shopping AdWords Labels
                        'New',  # Google Shopping Condition
                        'FALSE',  # Google Shopping Custom Product
                        product_type,  # Google Shopping Custom Label 0
                        'Luxury',  # Google Shopping Custom Label 1
                        product['color_value'] if product['color_value'] else '',  # Google Shopping Custom Label 2
                        'ChrisBella',  # Google Shopping Custom Label 3
                        category,  # Google Shopping Custom Label 4
                        product['variant_image'] if has_variant_image else '',  # Variant Image
                        'kg',  # Variant Weight Unit
                        'active'  # Status
                    ]
                else:
                    # Variant row with minimal details (like SHOPIFY-READY.csv)
                    row = [
                        handle,  # Handle
                        '',  # Title (empty for variants)
                        '',  # Body (HTML) (empty for variants)
                        'ChrisBella',  # Vendor
                        '',  # Product Category (empty for variants)
                        '',  # Type (empty for variants)
                        '',  # Tags (empty for variants)
                        '',  # Published (empty for variants)
                        'Color',  # Option1 Name
                        product['color_value'],  # Option1 Value
                        '',  # Option2 Name
                        '',  # Option2 Value
                        '',  # Option3 Name
                        '',  # Option3 Value
                        product['sku'],  # Variant SKU
                        '0',  # Variant Grams
                        'shopify',  # Variant Inventory Tracker
                        '100',  # Variant Inventory Qty
                        'deny',  # Variant Inventory Policy
                        'manual',  # Variant Fulfillment Service
                        product['price'],  # Variant Price
                        '',  # Variant Compare At Price
                        'TRUE',  # Variant Requires Shipping
                        'TRUE',  # Variant Taxable
                        product['gtin'],  # Variant Barcode
                        product['variant_image'] if has_variant_image else '',  # Image Src
                        str(i + 1) if has_variant_image else '',  # Image Position
                        f"{main_product['title']} {product['color_value']}".strip(),  # Image Alt Text
                        'FALSE',  # Gift Card
                        '',  # SEO Title (empty for variants)
                        '',  # SEO Description (empty for variants)
                        '',  # Google Shopping Category (empty for variants)
                        '',  # Google Shopping Gender (empty for variants)
                        '',  # Google Shopping Age Group (empty for variants)
                        '',  # Google Shopping MPN (empty for variants)
                        '',  # Google Shopping AdWords Grouping (empty for variants)
                        '',  # Google Shopping AdWords Labels (empty for variants)
                        '',  # Google Shopping Condition (empty for variants)
                        '',  # Google Shopping Custom Product (empty for variants)
                        '',  # Google Shopping Custom Label 0 (empty for variants)
                        '',  # Google Shopping Custom Label 1 (empty for variants)
                        product['color_value'] if product['color_value'] else '',  # Google Shopping Custom Label 2
                        '',  # Google Shopping Custom Label 3 (empty for variants)
                        '',  # Google Shopping Custom Label 4 (empty for variants)
                        product['variant_image'] if has_variant_image else '',  # Variant Image
                        'kg',  # Variant Weight Unit
                        'active'  # Status
                    ]
                
                writer.writerow(row)
        
        print(f"✅ Complete variant conversion finished!")
        print(f"📦 Total Products: {product_count}")
        print(f"🔄 Total Variants: {variant_count}")
        print(f"🛍️ Tote Bags: {tote_count}")
        print(f"📁 Output file: SHOPIFY-COMPLETE-VARIANTS.csv")

if __name__ == "__main__":
    main()
