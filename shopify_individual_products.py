#!/usr/bin/env python3
"""
Individual products conversion - each SKU is a separate product
"""

import csv
import re

def create_handle(title, sku):
    """Create a clean handle from title and SKU"""
    # Use English words for handle
    if 'سهرة' in title:
        base = 'evening-bag'
    elif 'كتف' in title:
        base = 'shoulder-bag'
    elif 'يد' in title and 'توتي' not in title:
        base = 'handbag'
    elif 'كروس' in title or 'كورس' in title:
        base = 'crossbody-bag'
    elif 'باوتش' in title:
        base = 'pouch-bag'
    elif 'توتي' in title:
        base = 'tote-bag'
    elif 'حروف' in title:
        base = 'letters'
    elif 'سيفين' in title:
        base = 'seven-palm'
    elif 'كرز' in title:
        base = 'cherry'
    elif 'جمل' in title:
        base = 'camel'
    else:
        base = 'bag'
    
    # Clean SKU for handle
    sku_clean = re.sub(r'[^\w]', '', str(sku).lower())
    return f"{base}-{sku_clean}"

def main():
    # Read original Salla export
    all_products = []
    
    with open('chrisbella_24-08-2025-12-00_jCmuZwaX8DClwrXDKhYVyL1rSD1f62pl9wUURec4_products.csv', 'r', encoding='utf-8') as infile:
        reader = csv.reader(infile)
        header = next(reader)  # Skip header
        
        for row in reader:
            if len(row) >= 24:  # Ensure we have enough columns
                # Extract relevant data from Salla format
                gtin = row[0]
                mpn = row[1]  # "منتج" or "خيار"
                title = row[2]  # Product title
                category = row[3] if len(row) > 3 else ''
                image_src = row[4] if len(row) > 4 else ''
                description = row[5] if len(row) > 5 else ''
                color_value = row[11] if len(row) > 11 else ''  # [2] القيمة (Color value)
                variant_image = row[12] if len(row) > 12 else ''  # [2] الصورة (Color image)
                price = row[21] if len(row) > 21 else ''  # السعر
                sku = row[23] if len(row) > 23 else ''  # رمز المنتج sku
                
                # Only process products with meaningful data
                if title and sku and price:
                    product = {
                        'gtin': gtin,
                        'mpn': mpn,
                        'title': title,
                        'category': category,
                        'image_src': image_src,
                        'description': description,
                        'color_value': color_value,
                        'variant_image': variant_image,
                        'price': price,
                        'sku': sku
                    }
                    all_products.append(product)
    
    # Shopify headers
    headers = [
        'Handle', 'Title', 'Body (HTML)', 'Vendor', 'Product Category', 'Type', 'Tags', 'Published',
        'Option1 Name', 'Option1 Value', 'Option2 Name', 'Option2 Value', 'Option3 Name', 'Option3 Value',
        'Variant SKU', 'Variant Grams', 'Variant Inventory Tracker', 'Variant Inventory Qty',
        'Variant Inventory Policy', 'Variant Fulfillment Service', 'Variant Price', 'Variant Compare At Price',
        'Variant Requires Shipping', 'Variant Taxable', 'Variant Barcode', 'Image Src', 'Image Position',
        'Image Alt Text', 'Gift Card', 'SEO Title', 'SEO Description', 'Google Shopping / Google Product Category',
        'Google Shopping / Gender', 'Google Shopping / Age Group', 'Google Shopping / MPN',
        'Google Shopping / AdWords Grouping', 'Google Shopping / AdWords Labels', 'Google Shopping / Condition',
        'Google Shopping / Custom Product', 'Google Shopping / Custom Label 0', 'Google Shopping / Custom Label 1',
        'Google Shopping / Custom Label 2', 'Google Shopping / Custom Label 3', 'Google Shopping / Custom Label 4',
        'Variant Image', 'Variant Weight Unit', 'Status'
    ]
    
    # Write the individual products Shopify CSV
    with open('SHOPIFY-INDIVIDUAL-PRODUCTS.csv', 'w', encoding='utf-8', newline='') as outfile:
        writer = csv.writer(outfile)
        writer.writerow(headers)
        
        product_count = 0
        tote_count = 0
        
        for product in all_products:
            product_count += 1
            
            # Count tote bags
            if 'توتي' in product['title']:
                tote_count += 1
            
            # Create handle
            handle = create_handle(product['title'], product['sku'])
            
            # Clean category
            category = product['category'] if product['category'] else 'حقائب'
            if 'أطفال' in category:
                category = 'حقائب'
            
            # Determine product type
            if 'توتي' in product['title']:
                product_type = 'Tote Bags'
            elif 'سهرة' in product['title']:
                product_type = 'Evening Bags'
            elif 'حروف' in product['title']:
                product_type = 'Letters'
            elif 'سيفين' in product['title'] or 'كرز' in product['title'] or 'جمل' in product['title']:
                product_type = 'Accessories'
            else:
                product_type = 'Bags'
            
            # Check if product has valid image
            has_image = (product['variant_image'] and 
                        product['variant_image'].startswith('http'))
            
            # Create full product title with color if available
            full_title = product['title']
            if product['color_value']:
                full_title = f"{product['title']} - {product['color_value']}"
            
            row = [
                handle,  # Handle
                full_title,  # Title
                product['description'],  # Body (HTML)
                'ChrisBella',  # Vendor
                category,  # Product Category
                product_type,  # Type
                'حقائب, إكسسوارات, نسائية',  # Tags
                'TRUE',  # Published
                'Color' if product['color_value'] else '',  # Option1 Name
                product['color_value'],  # Option1 Value
                '',  # Option2 Name
                '',  # Option2 Value
                '',  # Option3 Name
                '',  # Option3 Value
                product['sku'],  # Variant SKU
                '0',  # Variant Grams
                'shopify',  # Variant Inventory Tracker
                '100',  # Variant Inventory Qty
                'deny',  # Variant Inventory Policy
                'manual',  # Variant Fulfillment Service
                product['price'],  # Variant Price
                '',  # Variant Compare At Price
                'TRUE',  # Variant Requires Shipping
                'TRUE',  # Variant Taxable
                product['gtin'],  # Variant Barcode
                product['variant_image'] if has_image else '',  # Image Src
                '1' if has_image else '',  # Image Position
                f"{full_title}".strip(),  # Image Alt Text
                'FALSE',  # Gift Card
                f"{full_title} - ChrisBella",  # SEO Title
                f"{full_title} من ChrisBella",  # SEO Description
                'Apparel & Accessories > Handbags' if product_type in ['Bags', 'Tote Bags', 'Evening Bags'] else 'Arts & Entertainment > Hobbies & Creative Arts > Arts & Crafts',  # Google Shopping Category
                'Female',  # Google Shopping Gender
                'Adult',  # Google Shopping Age Group
                product['sku'],  # Google Shopping MPN
                product_type,  # Google Shopping AdWords Grouping
                'Luxury',  # Google Shopping AdWords Labels
                'New',  # Google Shopping Condition
                'FALSE',  # Google Shopping Custom Product
                product_type,  # Google Shopping Custom Label 0
                'Luxury',  # Google Shopping Custom Label 1
                product['color_value'] if product['color_value'] else '',  # Google Shopping Custom Label 2
                'ChrisBella',  # Google Shopping Custom Label 3
                category,  # Google Shopping Custom Label 4
                product['variant_image'] if has_image else '',  # Variant Image
                'kg',  # Variant Weight Unit
                'active'  # Status
            ]
            
            writer.writerow(row)
        
        print(f"✅ Individual products conversion finished!")
        print(f"📦 Total Products: {product_count}")
        print(f"🛍️ Tote Bags (توتي): {tote_count}")
        print(f"📁 Output file: SHOPIFY-INDIVIDUAL-PRODUCTS.csv")

if __name__ == "__main__":
    main()
